/* Footer Component Styles */
.footer {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
  position: relative;
  overflow: hidden;
  padding: 80px 0 0;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.gradientOrb1 {
  position: absolute;
  top: 10%;
  left: 5%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2), transparent);
  border-radius: 50%;
  animation: float 12s ease-in-out infinite;
}

.gradientOrb2 {
  position: absolute;
  top: 50%;
  right: 10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.2), transparent);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite reverse;
}

.gradientOrb3 {
  position: absolute;
  bottom: 20%;
  left: 50%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(236, 72, 153, 0.15), transparent);
  border-radius: 50%;
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Footer Content */
.footerContent {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr;
  gap: 60px;
  margin-bottom: 60px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.footerContent.fadeInUp {
  opacity: 1;
  transform: translateY(0);
}

/* Company Section */
.companySection {
  max-width: 400px;
}

.logoWrapper {
  margin-bottom: 30px;
}

.logo {
  width: auto;
  height: 60px;
  filter: brightness(1.1);
}

.vision {
  font-size: 1.1rem;
  color: #cbd5e1;
  line-height: 1.7;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Links Sections */
.linksSection {
  display: flex;
  flex-direction: column;
}

.sectionTitle {
  font-size: 1.4rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 25px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

.linksList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.linksList li {
  margin-bottom: 12px;
}

.footerLink {
  color: #94a3b8;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
}

.footerLink::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.footerLink:hover {
  color: #60a5fa;
  transform: translateX(5px);
}

.footerLink:hover::before {
  width: 100%;
}

/* Contact Section */
.contactSection {
  display: flex;
  flex-direction: column;
}

.contactGroup {
  margin-bottom: 25px;
}

.contactSubtitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 10px;
}

.contactInfo {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 8px;
}

.contactInfo i {
  color: #3b82f6;
  font-size: 1.1rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.addressText {
  color: #94a3b8;
  line-height: 1.5;
}

.addressText p {
  margin: 0;
  font-size: 0.95rem;
}

.contactLink {
  color: #94a3b8;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.contactLink:hover {
  color: #60a5fa;
}

/* Footer Bottom */
.footerBottom {
  padding: 30px 0;
}

.divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), rgba(139, 92, 246, 0.3), transparent);
  margin-bottom: 30px;
}

.copyright {
  text-align: center;
}

.copyright p {
  color: #64748b;
  font-size: 0.9rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footerContent {
    grid-template-columns: 1fr 1fr;
    gap: 40px;
  }
  
  .companySection {
    grid-column: 1 / -1;
    max-width: none;
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 60px 0 0;
  }
  
  .footerContent {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }
  
  .container {
    padding: 0 15px;
  }
  
  .sectionTitle {
    font-size: 1.2rem;
  }
  
  .vision {
    font-size: 1rem;
  }
  
  .logo {
    height: 50px;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 40px 0 0;
  }
  
  .footerContent {
    gap: 30px;
    margin-bottom: 30px;
  }
  
  .contactInfo {
    flex-direction: column;
    gap: 8px;
  }
  
  .contactInfo i {
    margin-top: 0;
  }
  
  .footerBottom {
    padding: 20px 0;
  }
}

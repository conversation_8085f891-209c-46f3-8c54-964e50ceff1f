/* Compare & Choose Section Styles */
.compareChooseSection {
  padding: 80px 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
  position: relative;
  overflow: hidden;
}

/* Background Elements */
.backgroundElements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.gradientOrb1 {
  position: absolute;
  top: 10%;
  left: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.3), transparent);
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
}

.gradientOrb2 {
  position: absolute;
  bottom: 20%;
  right: 10%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(139, 92, 246, 0.3), transparent);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite reverse;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

/* Header Section */
.headerSection {
  text-align: center;
  margin-bottom: 60px;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s ease-out;
}

.headerSection.fadeInUp {
  opacity: 1;
  transform: translateY(0);
}

.badge {
  display: inline-block;
  padding: 12px 24px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 50px;
  color: #3b82f6;
  font-size: 0.9rem;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
}

.mainTitle {
  font-size: 4rem;
  font-weight: 900;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #60a5fa, #a78bfa, #f472b6);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  animation: gradientShift 3s ease-in-out infinite;
  text-shadow: 0 0 30px rgba(96, 165, 250, 0.5);
  position: relative;
  letter-spacing: -0.02em;
}

.mainTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
  border-radius: 2px;
  animation: underlineExpand 2s ease-out 0.5s both;
}

.subtitle {
  font-size: 2.2rem;
  font-weight: 700;
  color: #f1f5f9;
  margin-bottom: 20px;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.description {
  font-size: 1.3rem;
  color: #cbd5e1;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.7;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Table Wrapper */
.tableWrapper {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  margin-bottom: 40px;
}

.tableContainer {
  overflow-x: auto;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Comparison Table */
.comparisonTable {
  width: 100%;
  min-width: 1000px;
  border-collapse: collapse;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-radius: 15px;
  overflow: hidden;
}

.comparisonTable thead {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.featureHeader {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: #ffffff;
  font-weight: 700;
  font-size: 1.1rem;
  padding: 20px 15px;
  text-align: left;
  border-right: 2px solid rgba(255, 255, 255, 0.1);
  min-width: 180px;
  position: sticky;
  left: 0;
  z-index: 10;
}

.countryHeader {
  color: #ffffff;
  font-weight: 600;
  font-size: 1rem;
  padding: 20px 15px;
  text-align: center;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 140px;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.countryHeaderContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.flagImage {
  width: 40px;
  height: 30px;
  object-fit: cover;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.countryName {
  font-size: 0.9rem;
  line-height: 1.2;
  text-align: center;
}

/* Table Body */
.dataRow {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: background-color 0.3s ease;
}

.dataRow:hover {
  background: rgba(255, 255, 255, 0.05);
}

.featureCell {
  background: linear-gradient(135deg, #475569 0%, #64748b 100%);
  color: #ffffff;
  font-weight: 600;
  font-size: 1rem;
  padding: 18px 15px;
  border-right: 2px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  left: 0;
  z-index: 5;
}

.dataCell {
  color: #e2e8f0;
  font-size: 0.9rem;
  padding: 18px 15px;
  text-align: center;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  vertical-align: middle;
}

.cellContent {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cellLine {
  line-height: 1.4;
}

/* Learn More Row */
.learnMoreRow {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  border-top: 2px solid rgba(59, 130, 246, 0.3);
}

.exploreBtn {
  background: linear-gradient(135deg, #2b70fa 0%, #4a90ff 50%, #6ba3ff 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 50px;
  font-weight: 700;
  font-size: 0.85rem;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(43, 112, 250, 0.4), 0 0 20px rgba(74, 144, 255, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.exploreBtn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.exploreBtn:hover::before {
  left: 100%;
}

.exploreBtn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(43, 112, 250, 0.6), 0 0 30px rgba(74, 144, 255, 0.4);
}

/* Disclaimer */
.disclaimer {
  text-align: center;
  margin-top: 20px;
}

.disclaimer p {
  color: #94a3b8;
  font-size: 0.9rem;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .compareChooseSection {
    padding: 80px 0;
  }

  .container {
    padding: 0 15px;
  }

  .mainTitle {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.8rem;
  }

  .description {
    font-size: 1.1rem;
  }

  .tableWrapper {
    padding: 20px;
  }

  .comparisonTable {
    min-width: 800px;
  }

  .featureHeader,
  .featureCell {
    min-width: 140px;
    font-size: 0.9rem;
  }

  .countryHeader {
    min-width: 120px;
    padding: 15px 10px;
  }

  .flagImage {
    width: 35px;
    height: 25px;
  }

  .countryName {
    font-size: 0.8rem;
  }

  .dataCell {
    padding: 15px 10px;
    font-size: 0.85rem;
  }

  .exploreBtn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .mainTitle {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1.5rem;
  }

  .tableWrapper {
    padding: 15px;
  }

  .comparisonTable {
    min-width: 700px;
  }

  .featureHeader,
  .featureCell {
    min-width: 120px;
    padding: 12px 8px;
  }

  .countryHeader {
    min-width: 100px;
    padding: 12px 8px;
  }

  .dataCell {
    padding: 12px 8px;
  }
}

/* Keyframe Animations */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes underlineExpand {
  0% { width: 0; }
  100% { width: 200px; }
}

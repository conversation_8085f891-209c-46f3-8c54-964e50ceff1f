'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import styles from './Footer.module.css';

export default function Footer() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  return (
    <footer className={styles.footer}>
      {/* Background Elements */}
      <div className={styles.backgroundElements}>
        <div className={styles.gradientOrb1}></div>
        <div className={styles.gradientOrb2}></div>
        <div className={styles.gradientOrb3}></div>
      </div>

      <div className={styles.container}>
        {/* Main Footer Content */}
        <div className={`${styles.footerContent} ${isVisible ? styles.fadeInUp : ''}`}>
          {/* Company Section */}
          <div className={styles.companySection}>
            <div className={styles.logoWrapper}>
              <Image
                src="/assets/logo.png"
                alt="Learn Education Logo"
                width={200}
                height={60}
                className={styles.logo}
              />
            </div>
            <p className={styles.vision}>
              Our vision is to be the UK's leading education consultancy, renowned for excellence and simplifying the admissions process for students worldwide.
            </p>
          </div>

          {/* Information Links */}
          <div className={styles.linksSection}>
            <h3 className={styles.sectionTitle}>Information</h3>
            <ul className={styles.linksList}>
              <li><a href="/about" className={styles.footerLink}>About Us</a></li>
              <li><a href="/privacy" className={styles.footerLink}>Privacy Policy</a></li>
              <li><a href="/contact" className={styles.footerLink}>Contact</a></li>
              <li><a href="/faq" className={styles.footerLink}>FAQ</a></li>
            </ul>
          </div>

          {/* Universities Links */}
          <div className={styles.linksSection}>
            <h3 className={styles.sectionTitle}>Universities</h3>
            <ul className={styles.linksList}>
              <li><a href="/uk-universities" className={styles.footerLink}>UK Universities</a></li>
              <li><a href="/usa-universities" className={styles.footerLink}>USA Universities</a></li>
              <li><a href="/ireland-universities" className={styles.footerLink}>Ireland Universities</a></li>
              <li><a href="/canada-universities" className={styles.footerLink}>Canada Universities</a></li>
              <li><a href="/germany-universities" className={styles.footerLink}>Germany Universities</a></li>
              <li><a href="/europe-universities" className={styles.footerLink}>Europe Universities</a></li>
              <li><a href="/australia-universities" className={styles.footerLink}>Australia Universities</a></li>
              <li><a href="/new-zealand-universities" className={styles.footerLink}>New Zealand</a></li>
            </ul>
          </div>

          {/* Contact Information */}
          <div className={styles.contactSection}>
            <h3 className={styles.sectionTitle}>Contact Us</h3>
            
            <div className={styles.contactGroup}>
              <h4 className={styles.contactSubtitle}>Headquarters:</h4>
              <div className={styles.contactInfo}>
                <i className="fas fa-map-marker-alt"></i>
                <div className={styles.addressText}>
                  <p>246-50 City Gate House,</p>
                  <p>Romford Road, 5th floor.</p>
                  <p>London. E7 9HZ</p>
                </div>
              </div>
            </div>

            <div className={styles.contactGroup}>
              <h4 className={styles.contactSubtitle}>Office:</h4>
              <div className={styles.contactInfo}>
                <i className="fas fa-map-marker-alt"></i>
                <div className={styles.addressText}>
                  <p>Millennium Market, 7th Floor.</p>
                  <p>Zindabazar Sylhet.</p>
                  <p>Bangladesh</p>
                </div>
              </div>
            </div>

            <div className={styles.contactGroup}>
              <div className={styles.contactInfo}>
                <i className="fas fa-phone"></i>
                <a href="tel:+447540258059" className={styles.contactLink}>+44 7540 258059</a>
              </div>
            </div>

            <div className={styles.contactGroup}>
              <div className={styles.contactInfo}>
                <i className="fas fa-envelope"></i>
                <a href="mailto:<EMAIL>" className={styles.contactLink}><EMAIL></a>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className={styles.footerBottom}>
          <div className={styles.divider}></div>
          <div className={styles.copyright}>
            <p>© 2025 Learn Education, All Rights Reserved.</p>
          </div>
        </div>
      </div>
    </footer>
  );
}
